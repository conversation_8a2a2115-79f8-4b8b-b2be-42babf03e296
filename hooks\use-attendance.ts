"use client"

import { useState, useEffect, useCallback } from "react"
import { toast } from "@/hooks/use-toast"
import {
  withRetry,
  getUserFriendlyError,
  logError,
} from "@/lib/error-handling"

export interface AttendanceRecord {
  id: string
  user_id: string
  date: string
  check_in_time?: string
  check_out_time?: string
  status: string
  hours_worked?: number
  notes?: string
  daily_sequence?: number
  is_active?: boolean
}

export interface AttendanceStatus {
  attendance: AttendanceRecord | null
  isCheckedIn: boolean
  todayEntries: AttendanceRecord[]
  totalHoursToday: number
  remainingCheckIns: number
  remainingCheckOuts: number
  activeSession: AttendanceRecord | null
  dailySummary: {
    completedSessions: number
    activeSessions: number
    formattedTotal: string
  }
  warnings: string[]
}

export interface UseAttendanceReturn {
  // State
  attendanceStatus: AttendanceStatus | null
  loading: boolean
  error: string | null
  
  // Actions
  checkIn: (notes?: string) => Promise<void>
  checkOut: (notes?: string) => Promise<void>
  refreshStatus: () => Promise<void>
  
  // Computed values
  isCheckedIn: boolean
  canCheckIn: boolean
  canCheckOut: boolean
  currentWorkDuration: string
}

export function useAttendance(): UseAttendanceReturn {
  const [attendanceStatus, setAttendanceStatus] = useState<AttendanceStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentWorkDuration, setCurrentWorkDuration] = useState<string>("00:00:00")

  // Fetch attendance status
  const fetchAttendanceStatus = useCallback(async () => {
    try {
      setError(null)
      const response = await fetch("/api/attendance/status", {
        credentials: "include",
      })
      const data = await response.json()

      if (data.success) {
        setAttendanceStatus(data)
      } else {
        throw new Error(data.error || "Failed to fetch attendance status")
      }
    } catch (error) {
      console.error("Error fetching attendance status:", error)
      setError("Failed to fetch attendance status")
    }
  }, [])

  // Calculate current work duration for active sessions
  const updateCurrentWorkDuration = useCallback(() => {
    if (attendanceStatus?.activeSession?.check_in_time) {
      const checkInTime = new Date(attendanceStatus.activeSession.check_in_time)
      const now = new Date()
      const diffMs = now.getTime() - checkInTime.getTime()
      
      const hours = Math.floor(diffMs / (1000 * 60 * 60))
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((diffMs % (1000 * 60)) / 1000)
      
      setCurrentWorkDuration(
        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      )
    } else {
      setCurrentWorkDuration("00:00:00")
    }
  }, [attendanceStatus?.activeSession?.check_in_time])

  // Check in function
  const checkIn = useCallback(async (notes?: string) => {
    if (notes && notes.length > 500) {
      toast({
        title: "Error",
        description: "Notes cannot exceed 500 characters",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    setError(null)
    
    try {
      const clockInOperation = async () => {
        const response = await fetch("/api/attendance/clock-in", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({ notes: notes?.trim() || undefined }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "Failed to clock in")
        }

        return response.json()
      }

      const data = await withRetry(clockInOperation, 2, 1000)

      if (data.success) {
        toast({
          title: "Success",
          description: data.message || "Successfully clocked in",
        })
        // Refresh status to get updated data
        await fetchAttendanceStatus()
      } else {
        throw new Error(data.error || "Failed to clock in")
      }
    } catch (error) {
      logError(error, "Dashboard clock in")
      const userFriendlyMessage = getUserFriendlyError(error)
      
      setError(userFriendlyMessage)
      toast({
        title: "Error",
        description: userFriendlyMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }, [fetchAttendanceStatus])

  // Check out function
  const checkOut = useCallback(async (notes?: string) => {
    if (notes && notes.length > 500) {
      toast({
        title: "Error",
        description: "Notes cannot exceed 500 characters",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    setError(null)
    
    try {
      const clockOutOperation = async () => {
        const response = await fetch("/api/attendance/clock-out", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({ notes: notes?.trim() || undefined }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "Failed to clock out")
        }

        return response.json()
      }

      const data = await withRetry(clockOutOperation, 2, 1000)

      if (data.success) {
        toast({
          title: "Success",
          description: data.message || "Successfully clocked out",
        })
        // Refresh status to get updated data
        await fetchAttendanceStatus()
      } else {
        throw new Error(data.error || "Failed to clock out")
      }
    } catch (error) {
      logError(error, "Dashboard clock out")
      const userFriendlyMessage = getUserFriendlyError(error)
      
      setError(userFriendlyMessage)
      toast({
        title: "Error",
        description: userFriendlyMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }, [fetchAttendanceStatus])

  // Refresh status function
  const refreshStatus = useCallback(async () => {
    await fetchAttendanceStatus()
  }, [fetchAttendanceStatus])

  // Initial fetch on mount
  useEffect(() => {
    fetchAttendanceStatus()
  }, [fetchAttendanceStatus])

  // Update work duration every second for active sessions
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null
    
    if (attendanceStatus?.isCheckedIn && attendanceStatus?.activeSession) {
      updateCurrentWorkDuration()
      interval = setInterval(updateCurrentWorkDuration, 1000)
    }
    
    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [attendanceStatus?.isCheckedIn, attendanceStatus?.activeSession, updateCurrentWorkDuration])

  // Computed values
  const isCheckedIn = attendanceStatus?.isCheckedIn || false
  const canCheckIn = !isCheckedIn && (attendanceStatus?.remainingCheckIns || 0) > 0
  const canCheckOut = isCheckedIn && (attendanceStatus?.remainingCheckOuts || 0) > 0

  return {
    attendanceStatus,
    loading,
    error,
    checkIn,
    checkOut,
    refreshStatus,
    isCheckedIn,
    canCheckIn,
    canCheckOut,
    currentWorkDuration,
  }
}
