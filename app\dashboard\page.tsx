"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { SimpleTaskManager } from "@/components/simple-task-manager"
import { HighPriorityReminders } from "@/components/high-priority-reminders"
import { FeatureDescription } from "@/components/feature-description"
import { ChatbotButton } from "@/components/chatbot-button"
import { useAuth } from "@/components/auth-provider"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/ui/chart"
import { Calendar, Clock, Users, TrendingUp, ArrowRight, Plus, CheckCircle, XCircle, Loader2, Timer } from "lucide-react"
import { EnhancedTaskModal } from "@/components/enhanced-task-modal"
import { TaskSearchFilter } from "@/components/task-search-filter"
import { TaskModal } from "@/components/task-modal"
import { useTasks, useCreateTask, useUpdateTask } from "@/hooks/use-tasks"
import { useRouter } from "next/navigation"
import { useAttendance } from "@/hooks/use-attendance"



export default function Dashboard() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("tasks")
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingTask, setEditingTask] = useState(null)
  const [taskFilters, setTaskFilters] = useState({})

  // Fetch tasks data using React Query
  const { data: tasksResponse, isLoading: tasksLoading, error: tasksError } = useTasks(taskFilters, { realTime: true })

  // Task mutations
  const createTaskMutation = useCreateTask()
  const updateTaskMutation = useUpdateTask()

  // Extract tasks from the response
  const tasks = tasksResponse?.data?.tasks || []

  // Attendance functionality
  const {
    attendanceStatus,
    loading: attendanceLoading,
    error: attendanceError,
    checkIn,
    checkOut,
    isCheckedIn,
    canCheckIn,
    canCheckOut,
    currentWorkDuration,
  } = useAttendance()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth/login")
    }
  }, [isLoading, isAuthenticated, router])

  if (isLoading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>
  }

  // Handle tasks error state
  if (tasksError) {
    console.error("Error loading tasks:", tasksError)
  }

  const handleAddTask = () => {
    setEditingTask(null)
    setIsModalOpen(true)
  }

  const handleEditTask = (task) => {
    setEditingTask(task)
    setIsModalOpen(true)
  }

  const handleDeleteTask = (taskId) => {
    // This will be handled by the delete mutation in the task card
    console.log("Delete task:", taskId)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setEditingTask(null)
  }

  const handleTaskClick = (task) => {
    setEditingTask(task)
    setIsModalOpen(true)
  }

  const handleSaveTask = async (taskData) => {
    try {
      if (editingTask) {
        // Update existing task
        await updateTaskMutation.mutateAsync({
          id: editingTask.id,
          data: {
            title: taskData.title,
            description: taskData.description,
            priority: taskData.priority,
            assigned_to: taskData.assigned_to || taskData.userId,
          }
        })
      } else {
        // Create new task
        await createTaskMutation.mutateAsync({
          title: taskData.title,
          description: taskData.description,
          priority: taskData.priority,
          assigned_to: taskData.assigned_to || taskData.userId,
        })
      }

      // Close modal on success
      setIsModalOpen(false)
      setEditingTask(null)
    } catch (error) {
      // Error handling is done in the mutation hooks
      console.error("Task save error:", error)
    }
  }

  // Attendance handlers
  const handleCheckIn = async () => {
    await checkIn()
  }

  const handleCheckOut = async () => {
    await checkOut()
  }

  const isAdmin = user?.role === "admin"
  const currentUserId = user?.id || ""

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pb-20 md:pb-6">
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-teal-800 dark:text-teal-300">Welcome, {user?.full_name || user?.email || "User"}</h1>
            <p className="text-gray-600 dark:text-gray-400">Here's what's happening today</p>
            {isCheckedIn && attendanceStatus?.activeSession && (
              <div className="flex items-center gap-2 mt-2">
                <Timer className="h-4 w-4 text-green-600" />
                <span className="text-sm text-green-600 font-medium">
                  Working: {currentWorkDuration}
                </span>
              </div>
            )}
          </div>
          <div className="flex items-center gap-2 mt-4 md:mt-0">
            <Button variant="outline" className="text-sm bg-transparent">
              <Calendar className="h-4 w-4 mr-2" />
              Today
            </Button>

            {/* Attendance Buttons */}
            {!isCheckedIn ? (
              <Button
                onClick={handleCheckIn}
                disabled={attendanceLoading || !canCheckIn}
                className="bg-exobank-green hover:bg-exobank-green/90 text-white text-sm"
              >
                {attendanceLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                {!canCheckIn && (attendanceStatus?.remainingCheckIns || 0) <= 0
                  ? "Daily Limit Reached"
                  : "Check In"
                }
              </Button>
            ) : (
              <Button
                onClick={handleCheckOut}
                disabled={attendanceLoading || !canCheckOut}
                className="bg-red-600 hover:bg-red-700 text-white text-sm border-red-600 hover:border-red-700"
              >
                {attendanceLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin text-white" />
                ) : (
                  <XCircle className="h-4 w-4 mr-2 text-white" />
                )}
                {!canCheckOut && (attendanceStatus?.remainingCheckOuts || 0) <= 0
                  ? "Daily Limit Reached"
                  : "Check Out"
                }
              </Button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          {/* Attendance Status Card */}
          <Card className="dark:bg-gray-800">
            <CardContent className="p-4">
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">Today's Attendance</span>
                <div className="flex items-end justify-between mt-1">
                  <div className="flex flex-col">
                    <span className="text-lg font-bold dark:text-white">
                      {isCheckedIn ? "Checked In" : "Not Checked In"}
                    </span>
                    {attendanceStatus?.dailySummary && (
                      <span className="text-xs text-gray-600 dark:text-gray-400">
                        {attendanceStatus.dailySummary.formattedTotal} worked
                      </span>
                    )}
                  </div>
                  <div className={`w-3 h-3 rounded-full ${isCheckedIn ? 'bg-green-500' : 'bg-gray-400'}`} />
                </div>
                {attendanceStatus?.remainingCheckIns !== undefined && (
                  <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                    {attendanceStatus.remainingCheckIns} check-ins remaining
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="dark:bg-gray-800">
            <CardContent className="p-4">
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">Total Leads</span>
                <div className="flex items-end justify-between mt-1">
                  <span className="text-2xl font-bold dark:text-white">127</span>
                  <span className="text-sm text-green-600 dark:text-green-400">+12% from last month</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="dark:bg-gray-800">
            <CardContent className="p-4">
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">Active Campaigns</span>
                <div className="flex items-end justify-between mt-1">
                  <span className="text-2xl font-bold dark:text-white">8</span>
                  <span className="text-sm text-green-600 dark:text-green-400">+2 new this week</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="dark:bg-gray-800">
            <CardContent className="p-4">
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">Conversion Rate</span>
                <div className="flex items-end justify-between mt-1">
                  <span className="text-2xl font-bold dark:text-white">24.5%</span>
                  <span className="text-sm text-red-600 dark:text-red-400">-3% from last month</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Attendance Warnings */}
        {attendanceStatus?.warnings && attendanceStatus.warnings.length > 0 && (
          <Card className="mb-6 border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-800">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 rounded-full bg-yellow-500 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-xs font-bold">!</span>
                </div>
                <div>
                  <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                    Attendance Notices
                  </h4>
                  <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                    {attendanceStatus.warnings.map((warning, index) => (
                      <li key={index}>• {warning}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Attendance Error Display */}
        {attendanceError && (
          <Card className="mb-6 border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <XCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-medium text-red-800 dark:text-red-200 mb-1">
                    Attendance Error
                  </h4>
                  <p className="text-sm text-red-700 dark:text-red-300">
                    {attendanceError}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Tabs defaultValue="tasks" className="mb-6" onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="tasks">Tasks</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="team">Team</TabsTrigger>
          </TabsList>

          <TabsContent value="tasks" className="mt-4">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
              <div className="lg:col-span-3">
                <Card className="dark:bg-gray-800">
                  <CardHeader className="pb-2 flex flex-row items-center justify-between">
                    <div>
                      <CardTitle className="text-lg text-teal-800 dark:text-teal-300">Task Management</CardTitle>
                      <CardDescription>Organize and track your marketing tasks</CardDescription>
                    </div>
                    <Button
                      size="sm"
                      onClick={handleAddTask}
                      className="bg-exobank-green hover:bg-exobank-green/90 text-white"
                    >
                      <Plus className="h-4 w-4 mr-1" /> Add Task
                    </Button>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <TaskSearchFilter
                      filters={taskFilters}
                      onFiltersChange={setTaskFilters}
                    />
                    {tasksLoading ? (
                      <div className="flex justify-center items-center py-12">
                        <div className="text-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600 mx-auto mb-4"></div>
                          <p className="text-gray-600 dark:text-gray-400">Loading tasks...</p>
                        </div>
                      </div>
                    ) : tasksError ? (
                      <div className="flex justify-center items-center py-12">
                        <div className="text-center">
                          <p className="text-red-600 dark:text-red-400 mb-2">Failed to load tasks</p>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">Please try refreshing the page</p>
                        </div>
                      </div>
                    ) : (
                      <SimpleTaskManager
                        onEditTask={handleEditTask}
                        onDeleteTask={handleDeleteTask}
                        isAdmin={user?.role === "admin" || user?.role === "hr_manager"}
                        currentUserId={user?.id}
                        filters={taskFilters}
                        realTimeUpdates={true}
                      />
                    )}
                  </CardContent>
                </Card>
              </div>

              <div className="lg:col-span-1">
                {tasksLoading ? (
                  <Card className="mb-4">
                    <CardHeader>
                      <CardTitle className="text-sm">Loading reminders...</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex justify-center py-4">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-teal-600"></div>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <HighPriorityReminders tasks={tasks} onTaskClick={handleTaskClick} />
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="mt-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              <Card className="dark:bg-gray-800 lg:col-span-2">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg text-teal-800 dark:text-teal-300">Campaign Performance</CardTitle>
                  <CardDescription>Monthly performance metrics</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <LineChart
                    data={[
                      { name: "Jan", Leads: 65, Conversions: 28 },
                      { name: "Feb", Leads: 59, Conversions: 24 },
                      { name: "Mar", Leads: 80, Conversions: 35 },
                      { name: "Apr", Leads: 81, Conversions: 32 },
                      { name: "May", Leads: 56, Conversions: 20 },
                      { name: "Jun", Leads: 55, Conversions: 21 },
                    ]}
                    index="name"
                    categories={["Leads", "Conversions"]}
                    colors={["#00cc00", "#0088ff"]}
                    valueFormatter={(value) => `${value}`}
                    yAxisWidth={40}
                    className="h-72"
                  />
                </CardContent>
              </Card>

              <Card className="dark:bg-gray-800">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg text-teal-800 dark:text-teal-300">Lead Sources</CardTitle>
                  <CardDescription>Where your leads come from</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <BarChart
                    data={[
                      { name: "Website", value: 45 },
                      { name: "Social", value: 30 },
                      { name: "Email", value: 15 },
                      { name: "Referral", value: 10 },
                    ]}
                    index="name"
                    categories={["value"]}
                    colors={["#00cc00"]}
                    valueFormatter={(value) => `${value}%`}
                    yAxisWidth={40}
                    className="h-72"
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="team" className="mt-4">
            <Card className="dark:bg-gray-800">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg text-teal-800 dark:text-teal-300">Team Members</CardTitle>
                <CardDescription>Your marketing team</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {[
                    { name: "John Doe", role: "Marketing Manager", tasks: 12 },
                    { name: "Jane Smith", role: "Content Writer", tasks: 8 },
                    { name: "Robert Johnson", role: "SEO Specialist", tasks: 10 },
                    { name: "Emily Davis", role: "Social Media Manager", tasks: 15 },
                    { name: "Michael Wilson", role: "Graphic Designer", tasks: 7 },
                    { name: "Sarah Brown", role: "Email Marketing", tasks: 9 },
                  ].map((member, index) => (
                    <Card key={index} className="dark:bg-gray-700">
                      <CardContent className="p-4">
                        <div className="flex flex-col items-center text-center">
                          <div className="w-16 h-16 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center mb-3">
                            <Users className="h-8 w-8 text-gray-500 dark:text-gray-400" />
                          </div>
                          <h3 className="font-medium dark:text-white">{member.name}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{member.role}</p>
                          <div className="mt-2 flex items-center text-sm">
                            <TrendingUp className="h-4 w-4 mr-1 text-exobank-green" />
                            <span>{member.tasks} active tasks</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Card className="dark:bg-gray-800 mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg text-teal-800 dark:text-teal-300">Featured Tools</CardTitle>
            <CardDescription>Explore our marketing tools</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              <FeatureDescription
                title="Lead Management"
                description="Track and manage potential customers"
                icon="Users"
                link="/leads"
              />
              <FeatureDescription
                title="Campaign Builder"
                description="Create and manage marketing campaigns"
                icon="Megaphone"
                link="/campaigns"
              />
              <FeatureDescription
                title="Analytics Dashboard"
                description="Visualize your marketing performance"
                icon="BarChart"
                link="/analytics"
              />
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="dark:bg-gray-800">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg text-teal-800 dark:text-teal-300">Recent Activities</CardTitle>
              <CardDescription>Latest updates from your team</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { user: "Jane Smith", action: "added a new lead", time: "2 hours ago" },
                  { user: "Robert Johnson", action: "completed task: Create social media posts", time: "3 hours ago" },
                  { user: "Emily Davis", action: "launched email campaign", time: "5 hours ago" },
                  { user: "Michael Wilson", action: "updated landing page design", time: "Yesterday" },
                ].map((activity, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center flex-shrink-0">
                      <Users className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                    </div>
                    <div>
                      <p className="text-sm">
                        <span className="font-medium dark:text-white">{activity.user}</span>{" "}
                        <span className="text-gray-600 dark:text-gray-400">{activity.action}</span>
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
              <Button variant="ghost" className="w-full mt-4 text-exobank-green">
                View All Activities <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </CardContent>
          </Card>

          <Card className="dark:bg-gray-800">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg text-teal-800 dark:text-teal-300">Upcoming Deadlines</CardTitle>
              <CardDescription>Tasks due soon</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { task: "Finalize Q3 Marketing Strategy", deadline: "Tomorrow", priority: "High" },
                  { task: "Review Social Media Analytics", deadline: "In 2 days", priority: "Medium" },
                  { task: "Prepare Monthly Report", deadline: "In 3 days", priority: "High" },
                  { task: "Update Customer Personas", deadline: "Next week", priority: "Low" },
                ].map((task, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium dark:text-white">{task.task}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Due: {task.deadline}</p>
                    </div>
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        task.priority === "High"
                          ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300"
                          : task.priority === "Medium"
                            ? "bg-warning/10 text-warning-foreground border border-warning/20"
                            : "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300"
                      }`}
                    >
                      {task.priority}
                    </span>
                  </div>
                ))}
              </div>
              <Button variant="ghost" className="w-full mt-4 text-exobank-green">
                View All Tasks <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      <ChatbotButton />
      {isModalOpen && (
        <TaskModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSave={handleSaveTask}
          task={editingTask}
          currentUserId={currentUserId}
        />
      )}

      {/* Enhanced Task Modal */}
      <EnhancedTaskModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        task={editingTask}
        currentUserId={user?.id}
      />
    </div>
  )
}
